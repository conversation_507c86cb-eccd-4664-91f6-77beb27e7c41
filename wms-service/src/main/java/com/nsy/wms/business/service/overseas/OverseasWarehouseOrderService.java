package com.nsy.wms.business.service.overseas;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.request.overseas.OverseasWarehouseOrderPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.overseas.OverseasWarehouseOrderResponse;
import com.nsy.api.wms.response.stockout.StatusCountResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.overseas.OverseasWarehouseOrderEntity;
import com.nsy.wms.repository.jpa.mapper.overseas.OverseasWarehouseOrderMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 海外仓订单服务类
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class OverseasWarehouseOrderService extends ServiceImpl<OverseasWarehouseOrderMapper, OverseasWarehouseOrderEntity> {


    @Resource
    private EnumConversionChineseUtils enumConversionChineseUtils;

    @Resource
    private LoginInfoService loginInfoService;

    /**
     * 分页查询
     */
    public PageResponse<OverseasWarehouseOrderResponse> queryByPage(OverseasWarehouseOrderPageRequest request) {
        PageResponse<OverseasWarehouseOrderResponse> pageResponse = new PageResponse<>();
        Page<OverseasWarehouseOrderEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<OverseasWarehouseOrderResponse> iPage = baseMapper.pageSearchOverseasWarehouseOrder(page, request);
        Map<String, String> statusMap = enumConversionChineseUtils.baseConversionByType(DictionaryNameEnum.WMS_OVERSEAS_ORDER_STATUS.getName());
        List<OverseasWarehouseOrderResponse> list = iPage.getRecords().stream().map(resp -> {
            resp.setStatusCn(statusMap.get(resp.getStatus()));
            return resp;
        }).collect(Collectors.toList());
        pageResponse.setContent(list);
        pageResponse.setTotalCount(iPage.getTotal());
        return pageResponse;
    }

    /**
     * 根据ID查询订单
     */
    public OverseasWarehouseOrderResponse getOneById(Integer id) {
        OverseasWarehouseOrderEntity entity = getById(id);
        if (Objects.isNull(entity)) {
            throw new InvalidRequestException("没有找到对应数据");
        }
        Map<String, String> statusMap = enumConversionChineseUtils.baseConversionByType(DictionaryNameEnum.WMS_OVERSEAS_ORDER_STATUS.getName());
        OverseasWarehouseOrderResponse resp = new OverseasWarehouseOrderResponse();
        BeanUtils.copyProperties(entity, resp);
        resp.setStatusCn(statusMap.get(entity.getStatus()));
        return resp;
    }

    /**
     * 根据状态统计订单数量
     */
    public List<StatusCountResponse> countByStatus() {
        // 获取数据库中的统计结果
        Map<String, List<StatusCountResponse>> collect = baseMapper.countByStatus().stream()
                .collect(Collectors.groupingBy(StatusCountResponse::getStatus));
        Map<String, String> statusMap = enumConversionChineseUtils.baseConversionByType(DictionaryNameEnum.WMS_OVERSEAS_ORDER_STATUS.getName());
        List<StatusCountResponse> list = new ArrayList<>();
        statusMap.forEach((k, v) -> {
            StatusCountResponse response = new StatusCountResponse();
            List<StatusCountResponse> responses = collect.get(k);
            response.setQty(CollectionUtils.isEmpty(responses) ? Integer.valueOf(0) : responses.get(0).getQty());
            response.setStatus(k);
            response.setValue(k);
            response.setLabel(v);
            list.add(response);
        });
        // 添加"所有"统计项
        StatusCountResponse allResponse = new StatusCountResponse();
        allResponse.setQty(list.stream().mapToInt(StatusCountResponse::getQty).sum());
        allResponse.setStatus("ALL");
        allResponse.setValue("ALL");
        allResponse.setLabel("所有");
        list.add(allResponse);
        return list;
    }

    /**
     * 更新订单状态
     */
    @Transactional(rollbackFor = Exception.class)
    public OverseasWarehouseOrderResponse updateStatus(Integer id, String status) {
        OverseasWarehouseOrderEntity entity = getById(id);
        if (Objects.isNull(entity)) {
            throw new InvalidRequestException("没有找到对应数据");
        }

        entity.setStatus(status);
        entity.setUpdateBy(loginInfoService.getName());
        updateById(entity);

        return getOneById(id);
    }




    /**
     * 删除海外仓订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
        OverseasWarehouseOrderEntity entity = getById(id);
        if (Objects.isNull(entity)) {
            throw new InvalidRequestException("没有找到对应数据");
        }

        removeById(id);
    }
}
