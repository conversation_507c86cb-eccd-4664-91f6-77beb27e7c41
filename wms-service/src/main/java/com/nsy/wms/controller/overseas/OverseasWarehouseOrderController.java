package com.nsy.wms.controller.overseas;

import com.nsy.api.wms.request.overseas.OverseasWarehouseOrderPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.overseas.OverseasWarehouseOrderResponse;
import com.nsy.api.wms.response.stockout.StatusCountResponse;
import com.nsy.wms.business.service.overseas.OverseasWarehouseOrderService;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.repository.entity.overseas.OverseasWarehouseOrderEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 海外仓订单控制器
 *
 * <AUTHOR>
 * @since 1.0
 */
@Api(tags = "海外仓订单接口")
@RestController
@RequestMapping("/overseas-order")
public class OverseasWarehouseOrderController extends BaseController {

    @Resource
    private OverseasWarehouseOrderService overseasWarehouseOrderService;

    @ApiOperation(value = "分页查询海外仓订单", notes = "支持多条件筛选和分页查询")
    @GetMapping("/page")
    public PageResponse<OverseasWarehouseOrderResponse> page(@Valid OverseasWarehouseOrderPageRequest request) {
        return overseasWarehouseOrderService.queryByPage(request);
    }

    @ApiOperation(value = "根据ID查询订单详情")
    @GetMapping("/{id}")
    public OverseasWarehouseOrderResponse getById(@PathVariable("id") Integer id) {
        return overseasWarehouseOrderService.getOneById(id);
    }

    @ApiOperation(value = "根据状态统计订单数量")
    @GetMapping("/count/status")
    public List<StatusCountResponse> countByStatus() {
        return overseasWarehouseOrderService.countByStatus();
    }

    @ApiOperation(value = "删除海外仓订单")
    @DeleteMapping("/{id}")
    public void delete(@PathVariable("id") Integer id) {
        overseasWarehouseOrderService.delete(id);
    }

    @ApiOperation(value = "更新订单状态")
    @PutMapping("/{id}/status")
    public OverseasWarehouseOrderResponse updateStatus(@PathVariable("id") Integer id, @ApiParam(value = "新状态", required = true) @RequestParam String status) {
        return overseasWarehouseOrderService.updateStatus(id, status);
    }
}
